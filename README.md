# Maze Agent UI

Maze Agent UI 是一个基于 React、TypeScript 和 Vite 的现代化项目。

## 功能特性

- 基于 React 19 和 TypeScript
- 使用 Vite 作为构建工具，提供快速的开发体验
- 集成 Ant Design 组件库
- 支持 Markdown 渲染
- 文件预览和处理功能
- 使用 ESLint 和 Prettier 进行代码规范化

## 快速开始

### 方式一：使用启动脚本（推荐）

```bash
pnpm start
```

### 方式二：手动启动

1. 安装依赖：

```bash
pnpm install
```

2. 启动开发服务器（同时启动前端和ServiceAdapter）：

```bash
pnpm run dev
```

3. 在浏览器中打开 http://localhost:3000 查看应用。

## ServiceAdapter服务

项目集成了CopilotKit ServiceAdapter服务，用于转发CopilotKit请求到后端Agno服务：

- **ServiceAdapter端口**: 4000
- **前端应用端口**: 3000
- **CopilotKit端点**: `/api`
- **后端Agno服务**: `https://agent-demo.8btc-ops.com/v2/agui`

### 环境变量配置

在 `.env.local` 文件中可以配置以下变量：

```env
SERVICE_ADAPTER_PORT=4000
AGNO_SERVICE_URL=https://agent-demo.8btc-ops.com/v2/agui
SERVICE_BASE_URL=https://agent-demo.8btc-ops.com
```

### 开发环境

- `pnpm start`: 使用启动脚本启动完整开发环境（推荐）
- `pnpm dev`: 同时启动ServiceAdapter和前端开发服务器
- `pnpm dev:client`: 仅启动前端开发服务器
- `pnpm dev:server`: 仅启动ServiceAdapter服务

### 生产环境

- `pnpm build`: 构建生产版本
- `pnpm start:prod`: 启动生产环境服务（包含前端和ServiceAdapter）
- `pnpm build:prod`: 构建并启动生产环境

### 工具脚本

- `pnpm health`: 检查服务健康状态
- `pnpm lint`: 运行 ESLint 检查
- `pnpm fix`: 自动修复 ESLint 问题
- `pnpm preview`: 预览生产构建

## 架构说明

```
┌─────────────────┐    ┌──────────────────┐    ┌─────────────────┐
│   前端应用      │    │  ServiceAdapter  │    │   后端Agno服务  │
│  (Port 3000)    │───▶│   (Port 4000)    │───▶│                 │
│                 │    │                  │    │                 │
│  CopilotKit     │    │  CopilotRuntime  │    │  AgnoAgent      │
│  runtimeUrl:    │    │  + AgnoAgent     │    │                 │
│  "/api"         │    │                  │    │                 │
└─────────────────┘    └──────────────────┘    └─────────────────┘
```

### 工作流程

1. 前端CopilotKit组件发送请求到 `/api`
2. Vite开发服务器将 `/api` 请求代理到 `http://localhost:4000`
3. ServiceAdapter接收请求，通过CopilotRuntime处理
4. CopilotRuntime使用AgnoAgent转发请求到后端Agno服务
5. 响应沿相反路径返回到前端

## Docker部署

### 使用Docker Compose（推荐）

```bash
# 构建并启动服务
docker-compose up --build

# 后台运行
docker-compose up -d --build

# 停止服务
docker-compose down
```

### 使用Docker

```bash
# 构建镜像
docker build -t maze-agent .

# 运行容器
docker run -p 4000:4000 \
  -e AGNO_SERVICE_URL=https://agent-demo.8btc-ops.com/v2/agui \
  maze-agent
```

部署后访问 `http://localhost:4000` 即可使用应用。

# 依赖文件
node_modules/
dist/
build/

# 配置文件
*.config.js
*.config.ts
vite.config.ts
tsconfig.json

# 锁定文件
package-lock.json
pnpm-lock.yaml
yarn.lock
*.lock

# 日志文件
*.log

# 临时文件
.DS_Store
.env*

# 编辑器文件
.vscode/
.idea/

# 静态资源文件
*.png
*.jpg
*.jpeg
*.gif
*.svg
*.ico
*.woff
*.woff2
*.ttf
*.eot

# 样式文件（如果不想格式化）
*.css

# 字体和图标文件
src/assets/relayFonts/
src/assets/icon/
src/assets/images/

# 其他
public/
*.md
*.html
*.json
*.js
*.xml
*.h
start.sh

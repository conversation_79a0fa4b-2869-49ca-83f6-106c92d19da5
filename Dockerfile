# 使用Node.js 18作为基础镜像
FROM node:18-alpine

# 设置工作目录
WORKDIR /app

# 安装pnpm
RUN npm install -g pnpm@7.33.1

# 复制package.json和pnpm-lock.yaml
COPY package.json pnpm-lock.yaml ./

# 安装依赖
RUN pnpm install --frozen-lockfile

# 复制源代码
COPY . .

# 构建应用
RUN pnpm run build

# 暴露端口
EXPOSE 4000

# 设置环境变量
ENV NODE_ENV=production
ENV SERVICE_ADAPTER_PORT=4000
ENV AGNO_SERVICE_URL=https://agent-demo.8btc-ops.com/v2/agui

# 启动生产服务
CMD ["pnpm", "run", "start:prod"]

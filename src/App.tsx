import React from 'react';
import { ConfigProvider } from 'antd';
import { RouterProvider } from 'react-router-dom';
import zhCN from 'antd/locale/zh_CN';
import router from './router';
import StoreProvider from './store/providers';
import { themeConfig } from './constants/theme';
import { CopilotKit } from '@copilotkit/react-core';

import '@copilotkit/react-ui/styles.css';

// App 组件：应用的根组件，设置全局配置和路由
const App: GenieType.FC = React.memo(() => {
  return (
    // 使用本地serviceAdapter服务
    <CopilotKit
      runtimeUrl="/agui"
      agent="agno_agent"
      showDevConsole={true}
      publicLicenseKey="ck_pub_48592d2b811d8a6383c3c868bcb84d64"
      headers={{
        Authorization: localStorage.getItem('token') || '',
      }}
    >
      <StoreProvider>
        <ConfigProvider locale={zhCN} theme={themeConfig}>
          <RouterProvider router={router} />
        </ConfigProvider>
      </StoreProvider>
    </CopilotKit>
  );
});

export default App;

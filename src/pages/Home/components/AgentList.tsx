import useSwr from 'swr';
import { agentServices } from '@/services/agent';
import { AgentDetails } from '@/types/agno';
import { Button } from 'antd';
import { SearchOutlined } from '@ant-design/icons';
import { useAtom } from 'jotai';
import { currentAgentAtom } from '@/store';
import { useEffect } from 'react';

type AgentListPropsType = Record<string, never>;

const AgentList: GenieType.FC<AgentListPropsType> = () => {
  const [currentAgent, setCurrentAgent] = useAtom(currentAgentAtom);
  const fetchAgentList = async (): Promise<AgentDetails[]> => {
    const response = await agentServices.agents();
    if (!Array.isArray(response)) {
      return [];
    }
    return response as AgentDetails[];
  };
  const { data: agentData } = useSwr<AgentDetails[]>(
    'getAgentList',
    fetchAgentList
  );

  useEffect(() => {
    if (agentData && agentData.length) {
      setCurrentAgent(agentData[0]);
    }
  }, [agentData, setCurrentAgent]);

  return (
    <div className="w-[95%] mx-auto mt-6 flex gap-8 flex-wrap">
      {agentData?.map(item => {
        return (
          <Button
            shape="round"
            icon={item.db_id === 'web_agent' && <SearchOutlined />}
            key={item.id}
            ghost
            type={currentAgent?.id === item.id ? 'primary' : 'default'}
            onClick={() => setCurrentAgent(item)}
          >
            {item.name}
          </Button>
        );
      })}
    </div>
  );
};

export default AgentList;

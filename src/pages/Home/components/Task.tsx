import { memo } from 'react';
import { CopilotChat } from '@copilotkit/react-ui';
import { themeCopilotKitConfig } from '@/constants/theme';
import classnames from 'classnames';
import {
  useCopilotContext,
  useCopilotChat,
  useCopilotMessagesContext,
} from '@copilotkit/react-core';
import AgentList from './AgentList';
import { SendOutlined } from '@ant-design/icons';

type TaskProps = Record<string, never>;

const Task: GenieType.FC<TaskProps> = memo(() => {
  const context = useCopilotContext();
  console.log('useCopilotContext', context);
  const chat = useCopilotChat();
  console.log('useCopilotChat', chat);
  const messages = useCopilotMessagesContext();
  console.log('useCopilotMessagesContext', messages);
  const isTaskStart = messages.messages.length > 0;
  return (
    <div
      style={themeCopilotKitConfig}
      className="h-full w-full max-w-[80%] mx-auto"
    >
      {!isTaskStart && (
        <div className="w-full text-white text-center leading-[130%] font-bold text-[38px] pt-[28vh]">
          Build the bridge from Web3 to AGI
          <br />
          Make Crypto Wisdom Eternal
        </div>
      )}
      <CopilotChat
        className={classnames('h-auto w-full', {
          'h-full': isTaskStart,
        })}
        imageUploadsEnabled
        inputFileAccept="image/png, image/jpeg, image/jpg, application/pdf"
        labels={{
          placeholder: 'How can I help you today?',
        }}
        icons={{
          sendIcon: <SendOutlined />,
        }}
      />
      {!isTaskStart && <AgentList />}
    </div>
  );
});

Task.displayName = 'Task';

export default Task;

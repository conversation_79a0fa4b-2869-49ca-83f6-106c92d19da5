import { API_HOST } from './constants';

const isDev = process.env.NODE_ENV === 'development';

const BASE_URL = isDev ? '/api' : API_HOST;

const _api = {
  // user
  send_email_code: `${BASE_URL}/user-manager/v1/user/send-email-code`,
  login_email: `${BASE_URL}/user-manager/v1/user/login-email`,
  user_info: `${BASE_URL}/user-manager/v1/user/get`,

  // app
  agents: `${BASE_URL}/v2/agents`,
  models: `${BASE_URL}/v2/models`,
  agent_detail: `${BASE_URL}/v2/agents/:id`,

  // sse
  chat: `${BASE_URL}/v2/agents/web_agent/runs`,
  chat_team: `${BASE_URL}/v2/teams/route_team/runs`,

  ag_ui: `${BASE_URL}/v2/agui`,

  // ag-ui-chat
  session: `${BASE_URL}/aguiapi/sessions/:sessionId`, // get
  sessions: `${BASE_URL}/aguiapi/sessions`, //get
  // ag-ui-server
  agent_run: `${BASE_URL}/aguiapi/:agentId/run`, // post
  session_run: `${BASE_URL}/aguiapi/sessions/:sessionId/runs`, // post
};

export default _api;

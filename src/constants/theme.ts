import { CopilotKitCSSProperties } from '@copilotkit/react-ui';

export const themeConfig = {
  token: {
    // Seed Token，影响范围大
    colorPrimary: '#D0FF35',
    // colorBgBase: "",
    colorTextBase: '#fff',
    fontFamily: 'Poppins',
  },
  components: {
    Input: {
      colorTextBase: '#fff',
    },
    Button: {
      colorPrimary: '#D0FF35',
      defaultBorderColor: '#434343',
      defaultGhostBorderColor: '#434343',
      defaultGhostColor: '#fafafa',
    },
    Dropdown: {
      colorBgElevated: '#1e1e1e', // 下拉菜单背景色
      colorText: '#fff', // 下拉文字颜色
    },
    Menu: {
      colorBgContainer: '#1e1e1e', // 菜单背景色
      colorItemBg: '#1e1e1e', // item 背景色
      colorText: '#fff', // 菜单文字色
      colorTextDisabled: '#888', // 禁用文字色
      colorItemTextHover: '#fff', // hover 时文字色
      colorItemBgHover: '#333', // hover 背景色
    },
    Popover: {
      colorBgElevated: '#1e1e1e', // Popover 背景
      colorText: '#fff', // Popover 文字颜色
      colorTextHeading: '#fff', // 标题颜色
      colorTextDescription: '#ccc', // 描述文字
    },
    Message: {
      colorBgElevated: '#1e1e1e', // Message 背景
      colorText: '#fff', // Message 文字颜色
    },
  },
};

export const themeCopilotKitConfig = {
  '--copilot-kit-primary-color': themeConfig.token.colorPrimary, // 主色
  '--copilot-kit-contrast-color': '#000', // 对比色
  '--copilot-kit-background-color': 'transparent', // 背景色
  '--copilot-kit-secondary-color': '#333', // 次要色
  '--copilot-kit-secondary-contrast-color': '#fff', // 次要对比色
  '--copilot-kit-input-background-color': '#141414', // 输入框背景色
  '--copilot-kit-separator-color': '#333', // 分割线、边框颜色
  '--copilot-kit-muted-color': '#888', // 次要文字色（禁用、不可用）
} as CopilotKitCSSProperties;

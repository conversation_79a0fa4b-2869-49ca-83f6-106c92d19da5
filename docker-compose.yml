version: '3.8'

services:
  maze-agent:
    build: .
    ports:
      - "4000:4000"
    environment:
      - NODE_ENV=production
      - SERVICE_ADAPTER_PORT=4000
      - AGNO_SERVICE_URL=https://agent-demo.8btc-ops.com/v2/agui
    restart: unless-stopped
    healthcheck:
      test: ["CMD", "node", "scripts/health-check.js"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

import winston from 'winston';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

// 创建日志目录
const logDir = path.join(__dirname, '../logs');

// 自定义日志格式
const logFormat = winston.format.combine(
  winston.format.timestamp({
    format: 'YYYY-MM-DD HH:mm:ss.SSS'
  }),
  winston.format.errors({ stack: true }),
  winston.format.json(),
  winston.format.prettyPrint()
);

// 控制台格式（更易读）
const consoleFormat = winston.format.combine(
  winston.format.colorize(),
  winston.format.timestamp({
    format: 'HH:mm:ss.SSS'
  }),
  winston.format.printf(({ timestamp, level, message, ...meta }) => {
    let metaStr = '';
    if (Object.keys(meta).length > 0) {
      metaStr = '\n' + JSON.stringify(meta, null, 2);
    }
    return `${timestamp} [${level}] ${message}${metaStr}`;
  })
);

// 创建Winston logger实例
const logger = winston.createLogger({
  level: process.env.LOG_LEVEL || 'info',
  format: logFormat,
  defaultMeta: { service: 'copilot-adapter' },
  transports: [
    // 错误日志文件
    new winston.transports.File({
      filename: path.join(logDir, 'error.log'),
      level: 'error',
      maxsize: 5242880, // 5MB
      maxFiles: 5,
    }),
    // 所有日志文件
    new winston.transports.File({
      filename: path.join(logDir, 'combined.log'),
      maxsize: 5242880, // 5MB
      maxFiles: 5,
    }),
    // 请求日志文件
    new winston.transports.File({
      filename: path.join(logDir, 'requests.log'),
      level: 'http',
      maxsize: 10485760, // 10MB
      maxFiles: 10,
    })
  ],
});

// 在开发环境中添加控制台输出
if (process.env.NODE_ENV !== 'production') {
  logger.add(new winston.transports.Console({
    format: consoleFormat
  }));
}

// 请求日志记录中间件
export const requestLogger = (req, res, next) => {
  const startTime = Date.now();
  const requestId = `req_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
  
  // 记录请求开始
  logger.http('请求开始', {
    requestId,
    method: req.method,
    url: req.url,
    headers: {
      'user-agent': req.get('User-Agent'),
      'content-type': req.get('Content-Type'),
      'authorization': req.get('Authorization') ? '[REDACTED]' : undefined,
    },
    ip: req.ip || req.connection.remoteAddress,
    body: req.body ? (typeof req.body === 'string' ? req.body.substring(0, 1000) : JSON.stringify(req.body).substring(0, 1000)) : null,
    timestamp: new Date().toISOString()
  });

  // 保存原始的res.end方法
  const originalEnd = res.end;
  
  // 重写res.end方法来记录响应
  res.end = function(chunk, encoding) {
    const duration = Date.now() - startTime;
    
    logger.http('请求完成', {
      requestId,
      statusCode: res.statusCode,
      duration: `${duration}ms`,
      contentLength: res.get('Content-Length') || (chunk ? chunk.length : 0),
      responseHeaders: {
        'content-type': res.get('Content-Type'),
        'content-length': res.get('Content-Length'),
      },
      timestamp: new Date().toISOString()
    });
    
    // 调用原始的end方法
    originalEnd.call(this, chunk, encoding);
  };
  
  // 记录请求ID到req对象中
  req.requestId = requestId;
  next();
};

// AgnoAgent请求拦截器
export const createAgnoAgentWithLogging = (config) => {
  const originalAgent = new (await import('@ag-ui/agno')).AgnoAgent(config);
  
  // 如果AgnoAgent有请求方法，我们可以包装它
  // 这里是一个示例，具体实现取决于AgnoAgent的API
  if (originalAgent.makeRequest) {
    const originalMakeRequest = originalAgent.makeRequest.bind(originalAgent);
    
    originalAgent.makeRequest = async (...args) => {
      const requestId = `agno_${Date.now()}_${Math.random().toString(36).substring(2, 11)}`;
      
      logger.info('AgnoAgent请求开始', {
        requestId,
        url: config.url,
        args: args.length > 0 ? JSON.stringify(args[0]).substring(0, 500) : null,
        timestamp: new Date().toISOString()
      });
      
      try {
        const result = await originalMakeRequest(...args);
        
        logger.info('AgnoAgent请求成功', {
          requestId,
          result: typeof result === 'string' ? result.substring(0, 500) : JSON.stringify(result).substring(0, 500),
          timestamp: new Date().toISOString()
        });
        
        return result;
      } catch (error) {
        logger.error('AgnoAgent请求失败', {
          requestId,
          error: error.message,
          stack: error.stack,
          timestamp: new Date().toISOString()
        });
        
        throw error;
      }
    };
  }
  
  return originalAgent;
};

export default logger;

import express from 'express';
import {
  CopilotRuntime,
  ExperimentalEmptyAdapter,
  copilotRuntimeNodeHttpEndpoint,
} from '@copilotkit/runtime';
import { AgnoAgent } from '@ag-ui/agno';
import logger, { requestLogger, createAgnoAgentWithLogging } from './logger.js';

const app = express();

// 添加JSON解析中间件（用于记录请求body）
app.use(express.json());

// 使用请求日志记录中间件
app.use(requestLogger);

// 使用空适配器，因为我们只使用一个代理
const serviceAdapter = new ExperimentalEmptyAdapter();

// 创建CopilotRuntime实例，配置Agno代理
const runtime = new CopilotRuntime({
  agents: {
    "agno_agent": new AgnoAgent({
      url: "https://agent-demo.8btc-ops.com/aguiapi/web_agent/run"
    }),
  },
});

// 记录服务启动信息
logger.info('CopilotRuntime初始化完成', {
  agentUrl: "https://agent-demo.8btc-ops.com/aguiapi/web_agent/run",
  timestamp: new Date().toISOString()
});

// 配置/api路由处理CopilotKit请求
app.use('/agui', (req, res, next) => {
  (async () => {
    const handler = copilotRuntimeNodeHttpEndpoint({
      endpoint: '/agui',
      runtime,
      serviceAdapter,
    });

    return handler(req, res);
  })().catch(next);
});

// 健康检查端点
app.get('/health', (req, res) => {
  res.json({ status: 'ok', timestamp: new Date().toISOString() });
});

const PORT = process.env.SERVICE_ADAPTER_PORT || 4000;

app.listen(PORT, () => {
  const startupInfo = {
    port: PORT,
    endpoint: `http://localhost:${PORT}/agui`,
    agnoServiceUrl: process.env.AGNO_SERVICE_URL || "https://agent-demo.8btc-ops.com/v2/agui",
    timestamp: new Date().toISOString(),
    nodeEnv: process.env.NODE_ENV || 'development'
  };

  logger.info('🚀 ServiceAdapter服务已启动', startupInfo);

  // 保留控制台输出以便快速查看
  console.log(`🚀 ServiceAdapter服务已启动，监听端口: ${PORT}`);
  console.log(`📡 CopilotKit端点: http://localhost:${PORT}/agui`);
  console.log(`🔗 Agno服务地址: ${process.env.AGNO_SERVICE_URL || "https://agent-demo.8btc-ops.com/v2/agui"}`);
  console.log(`📝 日志文件位置: ./logs/`);
});

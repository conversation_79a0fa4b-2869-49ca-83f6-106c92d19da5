import express from 'express';
import path from 'path';
import { fileURLToPath } from 'url';
import {
  CopilotRuntime,
  ExperimentalEmptyAdapter,
  copilotRuntimeNodeHttpEndpoint,
} from '@copilotkit/runtime';
import { AgnoAgent } from '@ag-ui/agno';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

const app = express();

// 使用空适配器，因为我们只使用一个代理
const serviceAdapter = new ExperimentalEmptyAdapter();

// 创建CopilotRuntime实例，配置Agno代理
const runtime = new CopilotRuntime({
  agents: {
    "agno_agent": new AgnoAgent({
      url: process.env.AGNO_SERVICE_URL || "https://agent-demo.8btc-ops.com/v2/agui"
    }),
  },
});

// 静态文件服务 - 服务前端构建文件
app.use(express.static(path.join(__dirname, '../dist')));

// 配置/api路由处理CopilotKit请求
app.use('/api', (req, res, next) => {
  (async () => {
    const handler = copilotRuntimeNodeHttpEndpoint({
      endpoint: '/api',
      runtime,
      serviceAdapter,
    });

    return handler(req, res);
  })().catch(next);
});

// 健康检查端点
app.get('/health', (req, res) => {
  res.json({ 
    status: 'ok', 
    timestamp: new Date().toISOString(),
    environment: 'production',
    agnoServiceUrl: process.env.AGNO_SERVICE_URL || "https://agent-demo.8btc-ops.com/v2/agui"
  });
});

// 所有其他路由返回前端应用
app.get('*', (req, res) => {
  res.sendFile(path.join(__dirname, '../dist/index.html'));
});

const PORT = process.env.PORT || process.env.SERVICE_ADAPTER_PORT || 4000;

app.listen(PORT, () => {
  console.log(`🚀 生产环境服务已启动，监听端口: ${PORT}`);
  console.log(`📡 CopilotKit端点: http://localhost:${PORT}/api`);
  console.log(`🌐 前端应用: http://localhost:${PORT}`);
  console.log(`🔗 Agno服务地址: ${process.env.AGNO_SERVICE_URL || "https://agent-demo.8btc-ops.com/v2/agui"}`);
});

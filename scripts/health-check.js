#!/usr/bin/env node

import http from 'http';

const colors = {
  red: '\x1b[31m',
  green: '\x1b[32m',
  yellow: '\x1b[33m',
  blue: '\x1b[34m',
  reset: '\x1b[0m'
};

function checkService(host, port, path, name) {
  return new Promise((resolve) => {
    const options = {
      hostname: host,
      port: port,
      path: path,
      method: 'GET',
      timeout: 5000
    };

    const req = http.request(options, (res) => {
      let data = '';
      res.on('data', (chunk) => {
        data += chunk;
      });
      res.on('end', () => {
        if (res.statusCode === 200) {
          console.log(`${colors.green}✅ ${name} 服务正常运行 (${host}:${port}${path})${colors.reset}`);
          resolve(true);
        } else {
          console.log(`${colors.red}❌ ${name} 服务响应异常 (状态码: ${res.statusCode})${colors.reset}`);
          resolve(false);
        }
      });
    });

    req.on('error', (err) => {
      console.log(`${colors.red}❌ ${name} 服务连接失败: ${err.message}${colors.reset}`);
      resolve(false);
    });

    req.on('timeout', () => {
      console.log(`${colors.yellow}⚠️  ${name} 服务响应超时${colors.reset}`);
      req.destroy();
      resolve(false);
    });

    req.end();
  });
}

async function main() {
  console.log(`${colors.blue}🔍 检查服务健康状态...${colors.reset}\n`);

  const services = [
    { host: 'localhost', port: 4000, path: '/health', name: 'ServiceAdapter' },
    { host: 'localhost', port: 3000, path: '/', name: '前端应用' }
  ];

  let allHealthy = true;

  for (const service of services) {
    const isHealthy = await checkService(service.host, service.port, service.path, service.name);
    if (!isHealthy) {
      allHealthy = false;
    }
  }

  console.log();
  if (allHealthy) {
    console.log(`${colors.green}🎉 所有服务运行正常！${colors.reset}`);
    process.exit(0);
  } else {
    console.log(`${colors.red}💥 部分服务存在问题，请检查日志${colors.reset}`);
    process.exit(1);
  }
}

main().catch(console.error);

#!/bin/bash

# 设置颜色输出
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

echo -e "${BLUE}🚀 启动Maze Agent开发环境...${NC}"

# 检查Node.js版本
if [ $(node -v | cut -d. -f1,2) -lt 18 ]; then
  echo -e "${RED}❌ Node.js版本18或更高版本是必需的。当前版本: $(node -v)${NC}"
  exit 1
fi

# 检查pnpm是否安装
if ! command -v pnpm &> /dev/null; then
  echo -e "${YELLOW}⚠️  pnpm未安装，正在安装pnpm...${NC}"
  npm install pnpm@7.33.1 -g
fi

# 检查pnpm版本
if [ $(pnpm -v | cut -d. -f1,2) -lt 7 ]; then
  echo -e "${RED}❌ pnpm版本7或更高版本是必需的。当前版本: $(pnpm -v)${NC}"
  exit 1
fi

# 安装依赖
echo -e "${YELLOW}📦 安装依赖...${NC}"
pnpm i --registry=https://registry.npmmirror.com

# 启动开发服务器
echo -e "${GREEN}🎯 启动开发服务器...${NC}"
echo -e "${BLUE}📡 ServiceAdapter将在端口4000启动${NC}"
echo -e "${BLUE}🌐 前端应用将在端口3000启动${NC}"

pnpm run dev

echo -e "${GREEN}✅ 开发环境启动成功！${NC}"
